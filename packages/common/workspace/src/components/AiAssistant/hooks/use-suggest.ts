import { useDebounceFn, useMemoizedFn, usePrevious, useUnmount, useUpdateEffect } from 'ahooks';
import { RefObject, useCallback, useEffect, useRef, useState } from 'react';
import useSWRMutation from 'swr/mutation';

import { convertInnerHtmlToText, moveCaretToEnd } from '../editable-div/helper';
import { chatApi } from '../chat-provider';
import { PageType, TEXT2UI_API_BASE } from '../constant';
import { PageArchitecture } from '../types';
import { useTracker } from '~workspace/hooks/use-tracker';
import { TrackServiceKey } from '@tencent/frontend-common/index';

interface SuggestParams {
  prompt: string;
  pageType?: PageType;
  update?: 1 | 0;
  pageArchitecture?: PageArchitecture;
}

function useSuggestApi({
  referenceContent,
  requestBody,
}: {
  referenceContent: string;
  requestBody: Omit<SuggestParams, 'prompt'> | (() => Omit<SuggestParams, 'prompt'>);
}) {
  const abortController = useRef<AbortController>();
  const prevReferenceContent = usePrevious(referenceContent);

  const abortRequest = useMemoizedFn(() => {
    if (abortController.current) {
      abortController.current.abort('suggest abort');
    }
  });

  useUnmount(() => {
    abortRequest();
  });

  const { data, trigger, isMutating } = useSWRMutation(
    `${TEXT2UI_API_BASE}/suggest`,
    async (url, { arg }: { arg: SuggestParams }) => {
      abortRequest();
      abortController.current = new AbortController();

      const res = await chatApi.baseRequest.jsonRequest(url, {
        method: 'POST',
        body: JSON.stringify(arg),
        signal: abortController.current.signal,
      });
      if (!res?.data || res.data.includes('null')) {
        return undefined;
      }

      return res.data.replace(/^["''"]|["''"]$/g, '') as string;
    },
  );
  const { run: debouncedTrigger } = useDebounceFn(trigger, { wait: 300, leading: true });

  useUpdateEffect(() => {
    const trimedRC = referenceContent?.trim();
    if (trimedRC && prevReferenceContent?.trim() !== trimedRC) {
      debouncedTrigger({ ...(typeof requestBody === 'function' ? requestBody() : requestBody), prompt: trimedRC });
    };
  }, [referenceContent]);

  return { data, isMutating, abortRequest };
}

const suggestPrefix = '<span id="text2ui-suggest" class="select-none pointer-events-none" style="color: #000;opacity: 0.5;">';
export default function useSuggest<T extends HTMLElement>(option: {
  inputRef: RefObject<T>;
  referenceContent: string;
  onApply?: (originValueWithSuggestion: string) => void;
  requestBody: Omit<SuggestParams, 'prompt'> | (() => Omit<SuggestParams, 'prompt'>);
}) {
  const { onApply, referenceContent, requestBody, inputRef } = option ?? {};
  const trackerService = useTracker();

  const isComposing = useRef(false);

  const [suggest, setSuggest] = useState<{
    show: boolean;
    content?: string;
  }>({ show: false, content: '' });

  const { data: suggestion, abortRequest } = useSuggestApi({
    referenceContent,
    requestBody,
  });

  useEffect(() => {
    if (suggestion) {
      showSuggest(suggestion);
    }
  }, [suggestion]);

  useEffect(() => {
    destroySuggest();
  }, [referenceContent]);

  const onApplyFn = useMemoizedFn(onApply ?? (() => {}));

  useEffect(() => {
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        e.preventDefault();
        let curInnerHTML = inputRef.current!.innerHTML;
        if (suggest.show && curInnerHTML.includes(suggestPrefix)) {
          curInnerHTML = curInnerHTML.slice(0, curInnerHTML.indexOf(suggestPrefix));
          onApplyFn(`${convertInnerHtmlToText(curInnerHTML)}${suggest.content}`);
        }
      }
      destroySuggest();
    };
    const handleBlur = () => {
      destroySuggest();
    };
    const handleMousedown = () => {
      destroySuggest();
    };
    const handleCompositionstart = () => {
      isComposing.current = true;
    };
    const handleCompositionend = () => {
      isComposing.current = false;
    };

    const inputEle = inputRef.current;

    inputEle?.addEventListener('keydown', handleKeydown);
    inputEle?.addEventListener('blur', handleBlur);
    inputEle?.addEventListener('mousedown', handleMousedown);
    inputEle?.addEventListener('compositionstart', handleCompositionstart);
    inputEle?.addEventListener('compositionend', handleCompositionend);
    return () => {
      inputEle?.removeEventListener('keydown', handleKeydown);
      inputEle?.removeEventListener('blur', handleBlur);
      inputEle?.removeEventListener('mousedown', handleMousedown);
      inputEle?.removeEventListener('compositionstart', handleCompositionstart);
      inputEle?.removeEventListener('compositionend', handleCompositionend);
    };
  }, [suggest]);

  const showSuggest = useCallback((text: string) => {
    if (!inputRef.current || document.activeElement !== inputRef.current || isComposing.current) return;

    let curInnerHTML = inputRef.current.innerHTML;
    const alreadyHasSuggest = curInnerHTML.includes(suggestPrefix);

    // 如果存在 suggest，则删除之前的 suggest
    if (alreadyHasSuggest) {
      curInnerHTML = curInnerHTML.slice(0, curInnerHTML.indexOf(suggestPrefix));
    }

    // 保存当前选区
    const selection = window.getSelection();
    const range = selection?.getRangeAt(0);

    // 更新 innerHTML
    inputRef.current.innerHTML = `${curInnerHTML ?? ''}${getSuggestHTML(text)}`;

    // 恢复光标位置到suggest之前
    if (range && selection) {
      const newRange = document.createRange();
      const textNodes = Array.from(inputRef.current.childNodes).filter(node => node.nodeType === Node.TEXT_NODE);
      const lastTextNode = textNodes[textNodes.length - 1];

      if (lastTextNode) {
        // 将光标设置在最后一个文本节点的末尾
        newRange.setStart(lastTextNode, lastTextNode.textContent?.length || 0);
      } else {
        // 如果没有文本节点，则将光标设置在容器开始位置
        newRange.setStart(inputRef.current, 0);
      }

      newRange.collapse(true);
      selection.removeAllRanges();
      selection.addRange(newRange);
    }

    trackerService.track(TrackServiceKey);
    setSuggest({ show: true, content: text });
  }, []);

  const destroySuggest = useCallback(() => {
    if (!suggest.show) return;

    let curInnerHTML = inputRef.current!.innerHTML;
    const alreadyHasSuggest = curInnerHTML.includes(suggestPrefix);
    // 如果存在 suggest，则删除之前的 suggest
    if (alreadyHasSuggest) {
      curInnerHTML = curInnerHTML.slice(0, curInnerHTML.indexOf(suggestPrefix));
    }
    inputRef.current!.innerHTML = curInnerHTML;
    moveCaretToEnd(inputRef.current!);

    setSuggest({ show: false, content: '' });
  }, [suggest.show]);

  const getSuggestHTML = (content: string) => {
    return `${suggestPrefix}  ${content}</span>`;
  };

  /** 去除传入的text中的suggest */
  const removeSuggestFormat = (valueHtml: string) => {
    let copyValueHtml = valueHtml;
    if (suggest.show && valueHtml.includes(suggestPrefix)) {
      copyValueHtml = valueHtml.slice(0, valueHtml.indexOf(suggestPrefix));
    }
    return copyValueHtml;
  };

  return {
    suggest,
    showSuggest,
    destroySuggest,
    removeSuggestFormat,
    abortRequest,
  };
}
